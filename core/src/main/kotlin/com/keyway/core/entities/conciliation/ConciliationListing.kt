package com.keyway.core.entities.conciliation

import com.keyway.core.entities.Money
import com.keyway.core.utils.DateUtils
import java.time.LocalDate
import java.time.OffsetDateTime

data class ConciliationEffective(
    val id: String?,
    val recordDate: LocalDate,
    val concessions: String?,
    val effectiveRent: Money,
    val effectiveDeposit: Money?,
    val createdAt: OffsetDateTime,
) {
    override fun equals(other: Any?): Boolean {
        if (other == null || other !is ConciliationEffective) return false
        return this.recordDate.plusDays(7).isAfter(other.recordDate) &&
            this.effectiveRent == other.effectiveRent &&
            (this.effectiveDeposit == null || other.effectiveDeposit == null || this.effectiveDeposit == other.effectiveDeposit)
    }

    override fun hashCode(): Int = super.hashCode()
}

class ConciliationListing(
    val id: String?,
    val propertyId: String,
    val typeId: String,
    val date: LocalDate,
    val recordSource: String,
    val rent: Money,
    val rentDeposit: Money?,
    val effective: ConciliationEffective,
    availableIn: LocalDate?,
    val createdAt: OffsetDateTime,
) {
    val availableIn: LocalDate? = availableIn?.takeIf { it.isAfter(DateUtils.now()) }

    override fun equals(other: Any?): Boolean {
        if (other == null || other !is ConciliationListing) return false
        return this.rent == other.rent &&
            this.date.plusDays(7).isAfter(other.date) &&
            (this.rentDeposit == null || other.rentDeposit == null || this.rentDeposit == other.rentDeposit)
    }

    override fun hashCode(): Int = super.hashCode()

    fun copy(
        id: String? = this.id,
        date: LocalDate = this.date,
        recordSource: String = this.recordSource,
        rent: Money = this.rent,
        rentDeposit: Money? = this.rentDeposit,
        conciliationEffective: ConciliationEffective = this.effective,
        availableIn: LocalDate? = this.availableIn,
        createdAt: OffsetDateTime = this.createdAt,
        propertyId: String = this.propertyId,
        typeId: String = this.typeId,
    ): ConciliationListing =
        ConciliationListing(
            id,
            propertyId,
            typeId,
            date,
            recordSource,
            rent,
            rentDeposit,
            conciliationEffective,
            availableIn,
            createdAt,
        )
}
